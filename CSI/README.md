# CSI - College Student Information System

A comprehensive Django-based student information system for managing students, teachers, and administrators.

## Features

- **Multi-user System**: Support for Students, Teachers, and Administrators
- **User Authentication**: Secure login/logout with role-based access
- **Student Management**: Student profiles, course enrollment, and grade tracking
- **Teacher Management**: Class management, student oversight, and grade assignment
- **Administrator Tools**: User management, course management, and system reports
- **Responsive Design**: Bootstrap-based UI that works on all devices

## Project Structure

```
CSI/
├── CSI/                    # Main project directory
│   ├── settings.py         # Django settings (configured for MySQL)
│   ├── urls.py            # Main URL configuration
│   └── ...
├── login/                  # Authentication and user management
├── students/              # Student-related functionality
├── teachers/              # Teacher-related functionality
├── administrators/        # Administrator functionality
├── templates/             # HTML templates
│   ├── base.html          # Base template
│   ├── login/             # Login app templates
│   ├── students/          # Student app templates
│   ├── teachers/          # Teacher app templates
│   └── administrators/    # Administrator app templates
├── manage.py              # Django management script
└── README.md              # This file
```

## Installation & Setup

### Prerequisites

- Python 3.8 or higher
- MySQL Server
- pip (Python package manager)

### Step 1: Clone and Setup Virtual Environment

```bash
# Navigate to project directory
cd CSI

# Activate virtual environment (already created)
venv\Scripts\activate  # Windows
# or
source venv/bin/activate  # Linux/Mac
```

### Step 2: Install Dependencies

Dependencies are already installed in the virtual environment:
- Django 5.2.4
- mysqlclient 2.2.7

### Step 3: Database Setup

1. **Create MySQL Database**:
   ```sql
   CREATE DATABASE csi_database;
   CREATE USER 'csi_user'@'localhost' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON csi_database.* TO 'csi_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

2. **Update Database Settings** (if needed):
   Edit `CSI/settings.py` and update the DATABASES configuration:
   ```python
   DATABASES = {
       'default': {
           'ENGINE': 'django.db.backends.mysql',
           'NAME': 'csi_database',
           'USER': 'csi_user',
           'PASSWORD': 'your_password',
           'HOST': 'localhost',
           'PORT': '3306',
       }
   }
   ```

### Step 4: Run Migrations

```bash
# Apply database migrations
python manage.py migrate

# Create sample data (optional but recommended)
python manage.py setup_sample_data
```

### Step 5: Create Superuser (if not using sample data)

```bash
python manage.py createsuperuser
```

### Step 6: Run the Development Server

```bash
python manage.py runserver
```

Visit `http://127.0.0.1:8000` in your browser.

## Default Login Credentials (Sample Data)

If you ran the `setup_sample_data` command, you can use these credentials:

- **Administrator**: 
  - Username: `admin`
  - Password: `admin123`

- **Teacher**: 
  - Username: `teacher1`
  - Password: `teacher123`

- **Student**: 
  - Username: `student1`
  - Password: `student123`

## Application Features

### Login App
- User authentication and session management
- Role-based redirects after login
- User profile management

### Students App
- Student dashboard with course overview
- Course enrollment and management
- Grade viewing and academic progress tracking

### Teachers App
- Teacher dashboard with class overview
- Student management for assigned courses
- Grade assignment and management

### Administrators App
- System dashboard with statistics
- User management (students, teachers, administrators)
- Course management
- System reports and settings

## URL Structure

- `/` - Home page
- `/login/` - User login
- `/logout/` - User logout
- `/register/` - User registration (placeholder)
- `/students/` - Student dashboard
- `/teachers/` - Teacher dashboard
- `/administrators/` - Administrator dashboard
- `/admin/` - Django admin interface

## Development Notes

- The project uses Django's built-in User model extended with UserProfile
- Bootstrap 5.1.3 is used for responsive design
- MySQL is configured as the primary database
- All apps follow Django best practices with proper separation of concerns

## Future Enhancements

- User registration functionality
- Email notifications
- File upload for assignments
- Calendar integration
- Advanced reporting features
- API endpoints for mobile app integration

## Troubleshooting

### Common Issues

1. **MySQL Connection Error**: Ensure MySQL server is running and credentials are correct
2. **Migration Issues**: Run `python manage.py makemigrations` then `python manage.py migrate`
3. **Static Files**: Run `python manage.py collectstatic` for production
4. **Permission Errors**: Ensure proper file permissions on the project directory

### Getting Help

- Check Django documentation: https://docs.djangoproject.com/
- Review error logs in the console
- Ensure all dependencies are installed correctly

## License

This project is created for educational purposes.
