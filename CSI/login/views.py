from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponse

# Create your views here.

def home(request):
    """Home page view"""
    return render(request, 'login/home.html')

def user_login(request):
    """User login view"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        user = authenticate(request, username=username, password=password)
        if user is not None:
            login(request, user)
            # Redirect based on user type
            if hasattr(user, 'userprofile'):
                user_type = user.userprofile.user_type
                if user_type == 'student':
                    return redirect('students:dashboard')
                elif user_type == 'teacher':
                    return redirect('teachers:dashboard')
                elif user_type == 'administrator':
                    return redirect('administrators:dashboard')
            return redirect('login:home')
        else:
            messages.error(request, 'Invalid username or password.')

    return render(request, 'login/login.html')

@login_required
def user_logout(request):
    """User logout view"""
    logout(request)
    messages.success(request, 'You have been logged out successfully.')
    return redirect('login:home')

def register(request):
    """User registration view"""
    if request.method == 'POST':
        # Registration logic will be implemented here
        messages.info(request, 'Registration functionality coming soon.')

    return render(request, 'login/register.html')
