from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.db import transaction
from login.models import UserProfile
from students.models import Student, Course, Enrollment
from teachers.models import Teacher, TeacherCourse
from administrators.models import Administrator, SystemSettings
from datetime import date, datetime


class Command(BaseCommand):
    help = 'Create sample data for CSI system'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Creating sample data...'))
        
        with transaction.atomic():
            # Create sample courses
            self.create_courses()
            
            # Create sample users
            self.create_admin_user()
            self.create_teacher_users()
            self.create_student_users()
            
            # Create enrollments
            self.create_enrollments()
            
            # Create system settings
            self.create_system_settings()
        
        self.stdout.write(self.style.SUCCESS('Sample data created successfully!'))
        self.stdout.write(self.style.WARNING('Default login credentials:'))
        self.stdout.write('Admin: admin/admin123')
        self.stdout.write('Teacher: teacher1/teacher123')
        self.stdout.write('Student: student1/student123')

    def create_courses(self):
        courses_data = [
            {'code': 'CS101', 'name': 'Introduction to Computer Science', 'credits': 3, 'desc': 'Basic programming concepts'},
            {'code': 'CS201', 'name': 'Data Structures', 'credits': 4, 'desc': 'Arrays, linked lists, trees, graphs'},
            {'code': 'CS301', 'name': 'Database Systems', 'credits': 3, 'desc': 'Relational databases and SQL'},
            {'code': 'MATH101', 'name': 'Calculus I', 'credits': 4, 'desc': 'Differential calculus'},
            {'code': 'ENG101', 'name': 'English Composition', 'credits': 3, 'desc': 'Writing and communication skills'},
        ]
        
        for course_data in courses_data:
            course, created = Course.objects.get_or_create(
                course_code=course_data['code'],
                defaults={
                    'course_name': course_data['name'],
                    'credits': course_data['credits'],
                    'description': course_data['desc'],
                    'is_active': True
                }
            )
            if created:
                self.stdout.write(f'Created course: {course.course_code}')

    def create_admin_user(self):
        # Create admin user
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'first_name': 'System',
                'last_name': 'Administrator',
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True
            }
        )
        if created:
            admin_user.set_password('admin123')
            admin_user.save()
            self.stdout.write('Created admin user')
        
        # Create user profile
        profile, created = UserProfile.objects.get_or_create(
            user=admin_user,
            defaults={
                'user_type': 'administrator',
                'phone_number': '555-0001',
                'date_of_birth': date(1980, 1, 1),
                'address': '123 Admin St, City, State'
            }
        )
        
        # Create administrator profile
        administrator, created = Administrator.objects.get_or_create(
            user=admin_user,
            defaults={
                'employee_id': 'ADM001',
                'department': 'Information Technology',
                'hire_date': date(2020, 1, 1),
                'access_level': 'super_admin'
            }
        )

    def create_teacher_users(self):
        teachers_data = [
            {'username': 'teacher1', 'first': 'John', 'last': 'Smith', 'email': '<EMAIL>', 'emp_id': 'TCH001', 'dept': 'Computer Science'},
            {'username': 'teacher2', 'first': 'Jane', 'last': 'Doe', 'email': '<EMAIL>', 'emp_id': 'TCH002', 'dept': 'Mathematics'},
        ]
        
        for teacher_data in teachers_data:
            user, created = User.objects.get_or_create(
                username=teacher_data['username'],
                defaults={
                    'first_name': teacher_data['first'],
                    'last_name': teacher_data['last'],
                    'email': teacher_data['email']
                }
            )
            if created:
                user.set_password('teacher123')
                user.save()
                self.stdout.write(f'Created teacher user: {user.username}')
            
            # Create user profile
            profile, created = UserProfile.objects.get_or_create(
                user=user,
                defaults={
                    'user_type': 'teacher',
                    'phone_number': '555-0002',
                    'date_of_birth': date(1975, 5, 15),
                    'address': '456 Teacher Ave, City, State'
                }
            )
            
            # Create teacher profile
            teacher, created = Teacher.objects.get_or_create(
                user=user,
                defaults={
                    'employee_id': teacher_data['emp_id'],
                    'department': teacher_data['dept'],
                    'hire_date': date(2018, 8, 15),
                    'specialization': 'Programming and Algorithms'
                }
            )

    def create_student_users(self):
        students_data = [
            {'username': 'student1', 'first': 'Alice', 'last': 'Johnson', 'email': '<EMAIL>', 'student_id': 'STU001', 'major': 'Computer Science'},
            {'username': 'student2', 'first': 'Bob', 'last': 'Wilson', 'email': '<EMAIL>', 'student_id': 'STU002', 'major': 'Mathematics'},
            {'username': 'student3', 'first': 'Carol', 'last': 'Brown', 'email': '<EMAIL>', 'student_id': 'STU003', 'major': 'Computer Science'},
        ]
        
        for student_data in students_data:
            user, created = User.objects.get_or_create(
                username=student_data['username'],
                defaults={
                    'first_name': student_data['first'],
                    'last_name': student_data['last'],
                    'email': student_data['email']
                }
            )
            if created:
                user.set_password('student123')
                user.save()
                self.stdout.write(f'Created student user: {user.username}')
            
            # Create user profile
            profile, created = UserProfile.objects.get_or_create(
                user=user,
                defaults={
                    'user_type': 'student',
                    'phone_number': '555-0003',
                    'date_of_birth': date(2000, 3, 10),
                    'address': '789 Student Rd, City, State'
                }
            )
            
            # Create student profile
            student, created = Student.objects.get_or_create(
                user=user,
                defaults={
                    'student_id': student_data['student_id'],
                    'enrollment_date': date(2023, 9, 1),
                    'graduation_year': 2027,
                    'major': student_data['major'],
                    'gpa': 3.5
                }
            )

    def create_enrollments(self):
        # Get some students and courses for enrollment
        students = Student.objects.all()[:2]
        courses = Course.objects.all()[:3]
        
        for student in students:
            for course in courses:
                enrollment, created = Enrollment.objects.get_or_create(
                    student=student,
                    course=course,
                    defaults={
                        'grade': 'A' if student.student_id == 'STU001' else 'B',
                        'is_completed': True
                    }
                )
                if created:
                    self.stdout.write(f'Created enrollment: {student.student_id} -> {course.course_code}')

    def create_system_settings(self):
        settings_data = [
            {'key': 'system_name', 'value': 'CSI - College Student Information System', 'desc': 'System display name'},
            {'key': 'maintenance_mode', 'value': 'false', 'desc': 'Enable/disable maintenance mode'},
            {'key': 'user_registration', 'value': 'false', 'desc': 'Allow user self-registration'},
            {'key': 'email_notifications', 'value': 'true', 'desc': 'Enable email notifications'},
        ]
        
        for setting_data in settings_data:
            setting, created = SystemSettings.objects.get_or_create(
                setting_key=setting_data['key'],
                defaults={
                    'setting_value': setting_data['value'],
                    'description': setting_data['desc']
                }
            )
            if created:
                self.stdout.write(f'Created setting: {setting.setting_key}')
