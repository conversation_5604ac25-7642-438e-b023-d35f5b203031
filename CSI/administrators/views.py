from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.http import HttpResponse
from .models import Administrator, SystemSettings
from students.models import Student, Course, Enrollment
from teachers.models import Teacher

# Create your views here.

@login_required
def admin_dashboard(request):
    """Administrator dashboard view"""
    try:
        administrator = Administrator.objects.get(user=request.user)
        # Get system statistics
        total_students = Student.objects.count()
        total_teachers = Teacher.objects.count()
        total_courses = Course.objects.count()
        total_enrollments = Enrollment.objects.count()

        context = {
            'administrator': administrator,
            'total_students': total_students,
            'total_teachers': total_teachers,
            'total_courses': total_courses,
            'total_enrollments': total_enrollments,
        }
        return render(request, 'administrators/dashboard.html', context)
    except Administrator.DoesNotExist:
        return HttpResponse("Administrator profile not found.", status=404)

@login_required
def manage_users(request):
    """Manage users view"""
    try:
        administrator = Administrator.objects.get(user=request.user)
        users = User.objects.all()
        students = Student.objects.all()
        teachers = Teacher.objects.all()

        context = {
            'administrator': administrator,
            'users': users,
            'students': students,
            'teachers': teachers,
        }
        return render(request, 'administrators/manage_users.html', context)
    except Administrator.DoesNotExist:
        return HttpResponse("Administrator profile not found.", status=404)

@login_required
def manage_courses(request):
    """Manage courses view"""
    try:
        administrator = Administrator.objects.get(user=request.user)
        courses = Course.objects.all()

        context = {
            'administrator': administrator,
            'courses': courses,
        }
        return render(request, 'administrators/manage_courses.html', context)
    except Administrator.DoesNotExist:
        return HttpResponse("Administrator profile not found.", status=404)

@login_required
def reports(request):
    """Reports view"""
    try:
        administrator = Administrator.objects.get(user=request.user)
        # Generate basic reports data
        enrollments = Enrollment.objects.all()

        context = {
            'administrator': administrator,
            'enrollments': enrollments,
        }
        return render(request, 'administrators/reports.html', context)
    except Administrator.DoesNotExist:
        return HttpResponse("Administrator profile not found.", status=404)

@login_required
def system_settings(request):
    """System settings view"""
    try:
        administrator = Administrator.objects.get(user=request.user)
        settings = SystemSettings.objects.all()

        context = {
            'administrator': administrator,
            'settings': settings,
        }
        return render(request, 'administrators/settings.html', context)
    except Administrator.DoesNotExist:
        return HttpResponse("Administrator profile not found.", status=404)
