#!/usr/bin/env python
"""
Setup script for CSI - College Student Information System
This script helps with initial project setup and database configuration.
"""

import os
import sys
import subprocess
import django
from django.core.management import execute_from_command_line

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def main():
    print("=" * 60)
    print("CSI - College Student Information System Setup")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not os.path.exists('manage.py'):
        print("Error: Please run this script from the CSI project directory")
        sys.exit(1)
    
    print("\n1. Checking Django installation...")
    try:
        import django
        print(f"✓ Django {django.get_version()} is installed")
    except ImportError:
        print("✗ Django is not installed. Please install requirements first:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    
    print("\n2. Setting up Django environment...")
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CSI.settings')
    django.setup()
    
    print("\n3. Running system checks...")
    if not run_command("python manage.py check", "System check"):
        print("Please fix the issues above before continuing")
        sys.exit(1)
    
    print("\n4. Creating database migrations...")
    if not run_command("python manage.py makemigrations", "Creating migrations"):
        print("Migration creation failed. Please check your models.")
        sys.exit(1)
    
    print("\n5. Applying database migrations...")
    if not run_command("python manage.py migrate", "Applying migrations"):
        print("Migration failed. Please check your database configuration.")
        print("\nMake sure:")
        print("- MySQL server is running")
        print("- Database 'csi_database' exists")
        print("- Database credentials in settings.py are correct")
        sys.exit(1)
    
    print("\n6. Creating sample data...")
    if run_command("python manage.py setup_sample_data", "Creating sample data"):
        print("\n" + "=" * 60)
        print("SETUP COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("\nDefault login credentials:")
        print("Administrator: admin / admin123")
        print("Teacher: teacher1 / teacher123")
        print("Student: student1 / student123")
        print("\nTo start the development server, run:")
        print("python manage.py runserver")
        print("\nThen visit: http://127.0.0.1:8000")
    else:
        print("\nSample data creation failed, but the basic setup is complete.")
        print("You can create a superuser manually with:")
        print("python manage.py createsuperuser")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
