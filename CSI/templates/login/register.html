{% extends 'base.html' %}

{% block title %}Register - CSI System{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">Register for CSI System</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-info" role="alert">
                    <strong>Registration Coming Soon!</strong><br>
                    Registration functionality will be implemented in the next phase. 
                    Please contact your administrator for account creation.
                </div>
                <form method="post">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="first_name" class="form-label">First Name</label>
                        <input type="text" class="form-control" id="first_name" name="first_name" disabled>
                    </div>
                    <div class="mb-3">
                        <label for="last_name" class="form-label">Last Name</label>
                        <input type="text" class="form-control" id="last_name" name="last_name" disabled>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" disabled>
                    </div>
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" disabled>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" disabled>
                    </div>
                    <div class="mb-3">
                        <label for="user_type" class="form-label">User Type</label>
                        <select class="form-control" id="user_type" name="user_type" disabled>
                            <option value="">Select User Type</option>
                            <option value="student">Student</option>
                            <option value="teacher">Teacher</option>
                            <option value="administrator">Administrator</option>
                        </select>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary" disabled>Register</button>
                    </div>
                </form>
                <hr>
                <div class="text-center">
                    <p>Already have an account? <a href="{% url 'login:user_login' %}">Login here</a></p>
                    <p><a href="{% url 'login:home' %}">Back to Home</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
