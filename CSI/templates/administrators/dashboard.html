{% extends 'base.html' %}

{% block title %}Administrator Dashboard - CSI System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2>Administrator Dashboard</h2>
        <p class="lead">Welcome, {{ administrator.user.get_full_name }}!</p>
    </div>
</div>

<div class="row">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <h5 class="card-title">Students</h5>
                <h2>{{ total_students }}</h2>
                <p class="card-text">Total registered students</p>
                <a href="{% url 'administrators:manage_users' %}" class="btn btn-light btn-sm">Manage</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <h5 class="card-title">Teachers</h5>
                <h2>{{ total_teachers }}</h2>
                <p class="card-text">Total active teachers</p>
                <a href="{% url 'administrators:manage_users' %}" class="btn btn-light btn-sm">Manage</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <h5 class="card-title">Courses</h5>
                <h2>{{ total_courses }}</h2>
                <p class="card-text">Total available courses</p>
                <a href="{% url 'administrators:manage_courses' %}" class="btn btn-light btn-sm">Manage</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <h5 class="card-title">Enrollments</h5>
                <h2>{{ total_enrollments }}</h2>
                <p class="card-text">Total course enrollments</p>
                <a href="{% url 'administrators:reports' %}" class="btn btn-light btn-sm">View Reports</a>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>Administrator Information</h5>
            </div>
            <div class="card-body">
                <p><strong>Employee ID:</strong> {{ administrator.employee_id }}</p>
                <p><strong>Department:</strong> {{ administrator.department }}</p>
                <p><strong>Access Level:</strong> {{ administrator.access_level|title }}</p>
                <p><strong>Hire Date:</strong> {{ administrator.hire_date }}</p>
                <p><strong>Status:</strong> 
                    {% if administrator.is_active %}
                        <span class="badge bg-success">Active</span>
                    {% else %}
                        <span class="badge bg-danger">Inactive</span>
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'administrators:manage_users' %}" class="btn btn-primary">Manage Users</a>
                    <a href="{% url 'administrators:manage_courses' %}" class="btn btn-success">Manage Courses</a>
                    <a href="{% url 'administrators:reports' %}" class="btn btn-info">View Reports</a>
                    <a href="{% url 'administrators:settings' %}" class="btn btn-secondary">System Settings</a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>System Overview</h5>
            </div>
            <div class="card-body">
                <p>System is running smoothly. All services are operational.</p>
                <div class="row">
                    <div class="col-md-4">
                        <p><strong>Database Status:</strong> <span class="badge bg-success">Online</span></p>
                    </div>
                    <div class="col-md-4">
                        <p><strong>Last Backup:</strong> Today, 2:00 AM</p>
                    </div>
                    <div class="col-md-4">
                        <p><strong>System Load:</strong> <span class="badge bg-info">Normal</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
