{% extends 'base.html' %}

{% block title %}Manage Users - CSI System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2>Manage Users</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'administrators:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item active">Manage Users</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4>{{ users.count }}</h4>
                            <p>Total Users</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4>{{ students.count }}</h4>
                            <p>Students</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4>{{ teachers.count }}</h4>
                            <p>Teachers</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <button class="btn btn-primary">Add New User</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>All Users</h5>
            </div>
            <div class="card-body">
                {% if users %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Username</th>
                                    <th>Full Name</th>
                                    <th>Email</th>
                                    <th>User Type</th>
                                    <th>Date Joined</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr>
                                    <td>{{ user.username }}</td>
                                    <td>{{ user.get_full_name|default:"N/A" }}</td>
                                    <td>{{ user.email|default:"N/A" }}</td>
                                    <td>
                                        {% if user.userprofile %}
                                            <span class="badge bg-info">{{ user.userprofile.user_type|title }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">No Profile</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ user.date_joined|date:"M d, Y" }}</td>
                                    <td>
                                        {% if user.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-danger">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary">View</button>
                                            <button class="btn btn-outline-warning">Edit</button>
                                            {% if user.is_active %}
                                                <button class="btn btn-outline-danger">Deactivate</button>
                                            {% else %}
                                                <button class="btn btn-outline-success">Activate</button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p>No users found.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
