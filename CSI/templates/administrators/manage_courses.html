{% extends 'base.html' %}

{% block title %}Manage Courses - CSI System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2>Manage Courses</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'administrators:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item active">Manage Courses</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4>{{ courses.count }}</h4>
                            <p>Total Courses</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4>{{ courses|length }}</h4>
                            <p>Active Courses</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <button class="btn btn-primary">Add New Course</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>All Courses</h5>
            </div>
            <div class="card-body">
                {% if courses %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Course Code</th>
                                    <th>Course Name</th>
                                    <th>Credits</th>
                                    <th>Description</th>
                                    <th>Created Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for course in courses %}
                                <tr>
                                    <td>{{ course.course_code }}</td>
                                    <td>{{ course.course_name }}</td>
                                    <td>{{ course.credits }}</td>
                                    <td>{{ course.description|truncatewords:8|default:"No description" }}</td>
                                    <td>{{ course.created_at|date:"M d, Y" }}</td>
                                    <td>
                                        {% if course.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-danger">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary">View</button>
                                            <button class="btn btn-outline-warning">Edit</button>
                                            {% if course.is_active %}
                                                <button class="btn btn-outline-danger">Deactivate</button>
                                            {% else %}
                                                <button class="btn btn-outline-success">Activate</button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        <h5>No Courses Found</h5>
                        <p>No courses have been created yet. Click "Add New Course" to create the first course.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
