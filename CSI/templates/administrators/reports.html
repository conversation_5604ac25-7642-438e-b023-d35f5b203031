{% extends 'base.html' %}

{% block title %}Reports - CSI System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2>Reports</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'administrators:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item active">Reports</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Enrollment Report</h5>
                <p class="card-text">View detailed enrollment statistics and trends.</p>
                <button class="btn btn-primary">Generate Report</button>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Grade Report</h5>
                <p class="card-text">Analyze grade distributions and academic performance.</p>
                <button class="btn btn-success">Generate Report</button>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">User Activity Report</h5>
                <p class="card-text">Monitor user login activity and system usage.</p>
                <button class="btn btn-info">Generate Report</button>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Enrollment Overview</h5>
            </div>
            <div class="card-body">
                {% if enrollments %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Student</th>
                                    <th>Course</th>
                                    <th>Enrollment Date</th>
                                    <th>Grade</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for enrollment in enrollments|slice:":10" %}
                                <tr>
                                    <td>{{ enrollment.student.user.get_full_name }}</td>
                                    <td>{{ enrollment.course.course_code }} - {{ enrollment.course.course_name }}</td>
                                    <td>{{ enrollment.enrollment_date }}</td>
                                    <td>{{ enrollment.grade|default:"In Progress" }}</td>
                                    <td>
                                        {% if enrollment.is_completed %}
                                            <span class="badge bg-success">Completed</span>
                                        {% else %}
                                            <span class="badge bg-primary">In Progress</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% if enrollments.count > 10 %}
                        <p class="text-muted">Showing 10 of {{ enrollments.count }} total enrollments.</p>
                    {% endif %}
                {% else %}
                    <div class="alert alert-info">
                        <h5>No Enrollment Data</h5>
                        <p>No enrollment data available to display in reports.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
