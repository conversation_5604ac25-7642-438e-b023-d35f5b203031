{% extends 'base.html' %}

{% block title %}System Settings - CSI System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2>System Settings</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'administrators:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item active">Settings</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5>System Configuration</h5>
            </div>
            <div class="card-body">
                {% if settings %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Setting</th>
                                    <th>Value</th>
                                    <th>Description</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for setting in settings %}
                                <tr>
                                    <td><strong>{{ setting.setting_key }}</strong></td>
                                    <td>{{ setting.setting_value|truncatewords:5 }}</td>
                                    <td>{{ setting.description|default:"No description" }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-warning">Edit</button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        <h5>No Settings Configured</h5>
                        <p>No system settings have been configured yet.</p>
                    </div>
                {% endif %}
                <button class="btn btn-primary">Add New Setting</button>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>Quick Settings</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">System Maintenance Mode</label>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="maintenanceMode">
                        <label class="form-check-label" for="maintenanceMode">
                            Enable Maintenance Mode
                        </label>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">User Registration</label>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="userRegistration">
                        <label class="form-check-label" for="userRegistration">
                            Allow User Registration
                        </label>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Email Notifications</label>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                        <label class="form-check-label" for="emailNotifications">
                            Enable Email Notifications
                        </label>
                    </div>
                </div>
                <button class="btn btn-success">Save Settings</button>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5>System Information</h5>
            </div>
            <div class="card-body">
                <p><strong>Django Version:</strong> 5.2.4</p>
                <p><strong>Database:</strong> MySQL</p>
                <p><strong>Last Backup:</strong> Today, 2:00 AM</p>
                <p><strong>System Status:</strong> <span class="badge bg-success">Online</span></p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
