<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}CSI - College Student Information System{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px 0;
            margin-top: 50px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{% url 'login:home' %}">CSI System</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if user.is_authenticated %}
                        {% if user.userprofile.user_type == 'student' %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'students:dashboard' %}">Dashboard</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'students:courses' %}">Courses</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'students:grades' %}">Grades</a>
                            </li>
                        {% elif user.userprofile.user_type == 'teacher' %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'teachers:dashboard' %}">Dashboard</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'teachers:classes' %}">Classes</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'teachers:manage_students' %}">Students</a>
                            </li>
                        {% elif user.userprofile.user_type == 'administrator' %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'administrators:dashboard' %}">Dashboard</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'administrators:manage_users' %}">Users</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'administrators:manage_courses' %}">Courses</a>
                            </li>
                        {% endif %}
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                {{ user.get_full_name|default:user.username }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#">Profile</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'login:user_logout' %}">Logout</a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'login:user_login' %}">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'login:register' %}">Register</a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        {% block content %}
        {% endblock %}
    </div>

    <footer class="footer mt-auto">
        <div class="container">
            <div class="text-center">
                <p>&copy; 2025 CSI - College Student Information System. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
