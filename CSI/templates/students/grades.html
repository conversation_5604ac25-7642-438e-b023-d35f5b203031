{% extends 'base.html' %}

{% block title %}My Grades - CSI System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2>My Grades</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'students:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item active">Grades</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Grade Report</h5>
            </div>
            <div class="card-body">
                {% if enrollments %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Course Code</th>
                                    <th>Course Name</th>
                                    <th>Credits</th>
                                    <th>Grade</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for enrollment in enrollments %}
                                <tr>
                                    <td>{{ enrollment.course.course_code }}</td>
                                    <td>{{ enrollment.course.course_name }}</td>
                                    <td>{{ enrollment.course.credits }}</td>
                                    <td>
                                        {% if enrollment.grade %}
                                            <span class="badge bg-success">{{ enrollment.grade }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Pending</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if enrollment.is_completed %}
                                            <span class="badge bg-success">Completed</span>
                                        {% else %}
                                            <span class="badge bg-primary">In Progress</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="mt-3">
                        <h6>Grade Summary</h6>
                        <p><strong>Current GPA:</strong> {{ student.gpa|default:"Not calculated" }}</p>
                        <p><strong>Total Credits Completed:</strong> 
                            {% widthratio enrollments|length 1 1 %} courses
                        </p>
                    </div>
                {% else %}
                    <p>No completed courses with grades available.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
