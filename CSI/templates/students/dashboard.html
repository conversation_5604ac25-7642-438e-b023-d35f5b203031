{% extends 'base.html' %}

{% block title %}Student Dashboard - CSI System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2>Student Dashboard</h2>
        <p class="lead">Welcome, {{ student.user.get_full_name }}!</p>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Student Information</h5>
                <p><strong>Student ID:</strong> {{ student.student_id }}</p>
                <p><strong>Major:</strong> {{ student.major|default:"Not specified" }}</p>
                <p><strong>GPA:</strong> {{ student.gpa|default:"N/A" }}</p>
                <p><strong>Enrollment Date:</strong> {{ student.enrollment_date }}</p>
                <a href="{% url 'students:profile' %}" class="btn btn-primary">View Profile</a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Current Courses</h5>
                <p><strong>Enrolled Courses:</strong> {{ enrollments.count }}</p>
                {% if enrollments %}
                    <ul class="list-unstyled">
                        {% for enrollment in enrollments|slice:":3" %}
                            <li>• {{ enrollment.course.course_name }}</li>
                        {% endfor %}
                        {% if enrollments.count > 3 %}
                            <li>• ... and {{ enrollments.count|add:"-3" }} more</li>
                        {% endif %}
                    </ul>
                {% else %}
                    <p>No courses enrolled yet.</p>
                {% endif %}
                <a href="{% url 'students:courses' %}" class="btn btn-success">View All Courses</a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Academic Progress</h5>
                <p><strong>Completed Courses:</strong> {{ enrollments|length }}</p>
                <p><strong>Current Semester:</strong> Spring 2025</p>
                <a href="{% url 'students:grades' %}" class="btn btn-info">View Grades</a>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Recent Activity</h5>
            </div>
            <div class="card-body">
                {% if enrollments %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Course Code</th>
                                    <th>Course Name</th>
                                    <th>Credits</th>
                                    <th>Grade</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for enrollment in enrollments|slice:":5" %}
                                <tr>
                                    <td>{{ enrollment.course.course_code }}</td>
                                    <td>{{ enrollment.course.course_name }}</td>
                                    <td>{{ enrollment.course.credits }}</td>
                                    <td>{{ enrollment.grade|default:"In Progress" }}</td>
                                    <td>
                                        {% if enrollment.is_completed %}
                                            <span class="badge bg-success">Completed</span>
                                        {% else %}
                                            <span class="badge bg-primary">In Progress</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p>No course enrollments found.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
