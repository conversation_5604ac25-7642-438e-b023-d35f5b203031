{% extends 'base.html' %}

{% block title %}My Courses - CSI System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2>My Courses</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'students:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item active">Courses</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Enrolled Courses</h5>
            </div>
            <div class="card-body">
                {% if enrollments %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Course Code</th>
                                    <th>Course Name</th>
                                    <th>Credits</th>
                                    <th>Enrollment Date</th>
                                    <th>Grade</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for enrollment in enrollments %}
                                <tr>
                                    <td>{{ enrollment.course.course_code }}</td>
                                    <td>{{ enrollment.course.course_name }}</td>
                                    <td>{{ enrollment.course.credits }}</td>
                                    <td>{{ enrollment.enrollment_date }}</td>
                                    <td>{{ enrollment.grade|default:"In Progress" }}</td>
                                    <td>
                                        {% if enrollment.is_completed %}
                                            <span class="badge bg-success">Completed</span>
                                        {% else %}
                                            <span class="badge bg-primary">In Progress</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p>You are not enrolled in any courses yet.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Available Courses</h5>
            </div>
            <div class="card-body">
                {% if available_courses %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Course Code</th>
                                    <th>Course Name</th>
                                    <th>Credits</th>
                                    <th>Description</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for course in available_courses %}
                                <tr>
                                    <td>{{ course.course_code }}</td>
                                    <td>{{ course.course_name }}</td>
                                    <td>{{ course.credits }}</td>
                                    <td>{{ course.description|truncatewords:10 }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary">Enroll</button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p>No courses available for enrollment.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
