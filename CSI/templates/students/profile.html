{% extends 'base.html' %}

{% block title %}Student Profile - CSI System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2>Student Profile</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'students:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item active">Profile</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5>Personal Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Full Name:</strong> {{ student.user.get_full_name }}</p>
                        <p><strong>Username:</strong> {{ student.user.username }}</p>
                        <p><strong>Email:</strong> {{ student.user.email }}</p>
                        <p><strong>Student ID:</strong> {{ student.student_id }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Major:</strong> {{ student.major|default:"Not specified" }}</p>
                        <p><strong>Enrollment Date:</strong> {{ student.enrollment_date }}</p>
                        <p><strong>Expected Graduation:</strong> {{ student.graduation_year|default:"Not set" }}</p>
                        <p><strong>Current GPA:</strong> {{ student.gpa|default:"N/A" }}</p>
                    </div>
                </div>
                <button class="btn btn-primary">Edit Profile</button>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>Quick Stats</h5>
            </div>
            <div class="card-body">
                <p><strong>Account Status:</strong> 
                    {% if student.is_active %}
                        <span class="badge bg-success">Active</span>
                    {% else %}
                        <span class="badge bg-danger">Inactive</span>
                    {% endif %}
                </p>
                <p><strong>Member Since:</strong> {{ student.created_at|date:"M Y" }}</p>
                <p><strong>Last Updated:</strong> {{ student.updated_at|date:"M d, Y" }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
