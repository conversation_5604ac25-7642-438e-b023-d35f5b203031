{% extends 'base.html' %}

{% block title %}Teacher Profile - CSI System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2>Teacher Profile</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'teachers:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item active">Profile</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5>Personal Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Full Name:</strong> {{ teacher.user.get_full_name }}</p>
                        <p><strong>Username:</strong> {{ teacher.user.username }}</p>
                        <p><strong>Email:</strong> {{ teacher.user.email }}</p>
                        <p><strong>Employee ID:</strong> {{ teacher.employee_id }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Department:</strong> {{ teacher.department }}</p>
                        <p><strong>Specialization:</strong> {{ teacher.specialization|default:"Not specified" }}</p>
                        <p><strong>Hire Date:</strong> {{ teacher.hire_date }}</p>
                        <p><strong>Status:</strong> 
                            {% if teacher.is_active %}
                                <span class="badge bg-success">Active</span>
                            {% else %}
                                <span class="badge bg-danger">Inactive</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
                <button class="btn btn-primary">Edit Profile</button>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>Account Details</h5>
            </div>
            <div class="card-body">
                <p><strong>Member Since:</strong> {{ teacher.created_at|date:"M Y" }}</p>
                <p><strong>Last Updated:</strong> {{ teacher.updated_at|date:"M d, Y" }}</p>
                <p><strong>Account Type:</strong> Teacher</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
