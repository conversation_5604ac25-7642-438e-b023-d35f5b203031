{% extends 'base.html' %}

{% block title %}My Classes - CSI System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2>My Classes</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'teachers:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item active">Classes</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Assigned Classes</h5>
            </div>
            <div class="card-body">
                {% if teacher_courses %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Course Code</th>
                                    <th>Course Name</th>
                                    <th>Credits</th>
                                    <th>Semester</th>
                                    <th>Year</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for tc in teacher_courses %}
                                <tr>
                                    <td>{{ tc.course.course_code }}</td>
                                    <td>{{ tc.course.course_name }}</td>
                                    <td>{{ tc.course.credits }}</td>
                                    <td>{{ tc.semester }}</td>
                                    <td>{{ tc.year }}</td>
                                    <td>
                                        {% if tc.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary">View</button>
                                            <button class="btn btn-outline-success">Students</button>
                                            <button class="btn btn-outline-warning">Grades</button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        <h5>No Classes Assigned</h5>
                        <p>You don't have any classes assigned yet. Please contact your administrator for course assignments.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
