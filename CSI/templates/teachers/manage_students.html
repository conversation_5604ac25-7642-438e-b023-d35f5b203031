{% extends 'base.html' %}

{% block title %}Manage Students - CSI System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2>Manage Students</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'teachers:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item active">Manage Students</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Students in My Courses</h5>
            </div>
            <div class="card-body">
                {% if enrollments %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Student Name</th>
                                    <th>Student ID</th>
                                    <th>Course</th>
                                    <th>Enrollment Date</th>
                                    <th>Current Grade</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for enrollment in enrollments %}
                                <tr>
                                    <td>{{ enrollment.student.user.get_full_name }}</td>
                                    <td>{{ enrollment.student.student_id }}</td>
                                    <td>{{ enrollment.course.course_code }} - {{ enrollment.course.course_name }}</td>
                                    <td>{{ enrollment.enrollment_date }}</td>
                                    <td>{{ enrollment.grade|default:"Not graded" }}</td>
                                    <td>
                                        {% if enrollment.is_completed %}
                                            <span class="badge bg-success">Completed</span>
                                        {% else %}
                                            <span class="badge bg-primary">In Progress</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary">View</button>
                                            <button class="btn btn-outline-warning">Grade</button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        <h5>No Students Found</h5>
                        <p>There are no students enrolled in your courses yet.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
