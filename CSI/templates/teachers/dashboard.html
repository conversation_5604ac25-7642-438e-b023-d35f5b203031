{% extends 'base.html' %}

{% block title %}Teacher Dashboard - CSI System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2>Teacher Dashboard</h2>
        <p class="lead">Welcome, {{ teacher.user.get_full_name }}!</p>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Teacher Information</h5>
                <p><strong>Employee ID:</strong> {{ teacher.employee_id }}</p>
                <p><strong>Department:</strong> {{ teacher.department }}</p>
                <p><strong>Specialization:</strong> {{ teacher.specialization|default:"Not specified" }}</p>
                <p><strong>Hire Date:</strong> {{ teacher.hire_date }}</p>
                <a href="{% url 'teachers:profile' %}" class="btn btn-primary">View Profile</a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Current Classes</h5>
                <p><strong>Active Courses:</strong> {{ teacher_courses.count }}</p>
                {% if teacher_courses %}
                    <ul class="list-unstyled">
                        {% for tc in teacher_courses|slice:":3" %}
                            <li>• {{ tc.course.course_name }}</li>
                        {% endfor %}
                        {% if teacher_courses.count > 3 %}
                            <li>• ... and {{ teacher_courses.count|add:"-3" }} more</li>
                        {% endif %}
                    </ul>
                {% else %}
                    <p>No active courses assigned.</p>
                {% endif %}
                <a href="{% url 'teachers:classes' %}" class="btn btn-success">View All Classes</a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Quick Actions</h5>
                <p><strong>Current Semester:</strong> Spring 2025</p>
                <div class="d-grid gap-2">
                    <a href="{% url 'teachers:manage_students' %}" class="btn btn-info btn-sm">Manage Students</a>
                    <a href="{% url 'teachers:manage_grades' %}" class="btn btn-warning btn-sm">Manage Grades</a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>My Courses</h5>
            </div>
            <div class="card-body">
                {% if teacher_courses %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Course Code</th>
                                    <th>Course Name</th>
                                    <th>Credits</th>
                                    <th>Semester</th>
                                    <th>Year</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for tc in teacher_courses %}
                                <tr>
                                    <td>{{ tc.course.course_code }}</td>
                                    <td>{{ tc.course.course_name }}</td>
                                    <td>{{ tc.course.credits }}</td>
                                    <td>{{ tc.semester }}</td>
                                    <td>{{ tc.year }}</td>
                                    <td>
                                        {% if tc.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p>No courses assigned yet.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
