from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse
from .models import Teacher, TeacherCourse
from students.models import Course, Student, Enrollment

# Create your views here.

@login_required
def teacher_dashboard(request):
    """Teacher dashboard view"""
    try:
        teacher = Teacher.objects.get(user=request.user)
        teacher_courses = TeacherCourse.objects.filter(teacher=teacher, is_active=True)
        context = {
            'teacher': teacher,
            'teacher_courses': teacher_courses,
        }
        return render(request, 'teachers/dashboard.html', context)
    except Teacher.DoesNotExist:
        return HttpResponse("Teacher profile not found.", status=404)

@login_required
def teacher_profile(request):
    """Teacher profile view"""
    try:
        teacher = Teacher.objects.get(user=request.user)
        context = {'teacher': teacher}
        return render(request, 'teachers/profile.html', context)
    except Teacher.DoesNotExist:
        return HttpResponse("Teacher profile not found.", status=404)

@login_required
def teacher_classes(request):
    """Teacher classes view"""
    try:
        teacher = Teacher.objects.get(user=request.user)
        teacher_courses = TeacherCourse.objects.filter(teacher=teacher, is_active=True)
        context = {
            'teacher': teacher,
            'teacher_courses': teacher_courses,
        }
        return render(request, 'teachers/classes.html', context)
    except Teacher.DoesNotExist:
        return HttpResponse("Teacher profile not found.", status=404)

@login_required
def manage_students(request):
    """Manage students view"""
    try:
        teacher = Teacher.objects.get(user=request.user)
        teacher_courses = TeacherCourse.objects.filter(teacher=teacher, is_active=True)
        # Get all students enrolled in teacher's courses
        enrollments = Enrollment.objects.filter(course__in=[tc.course for tc in teacher_courses])
        context = {
            'teacher': teacher,
            'enrollments': enrollments,
        }
        return render(request, 'teachers/manage_students.html', context)
    except Teacher.DoesNotExist:
        return HttpResponse("Teacher profile not found.", status=404)

@login_required
def manage_grades(request):
    """Manage grades view"""
    try:
        teacher = Teacher.objects.get(user=request.user)
        teacher_courses = TeacherCourse.objects.filter(teacher=teacher, is_active=True)
        enrollments = Enrollment.objects.filter(course__in=[tc.course for tc in teacher_courses])
        context = {
            'teacher': teacher,
            'enrollments': enrollments,
        }
        return render(request, 'teachers/manage_grades.html', context)
    except Teacher.DoesNotExist:
        return HttpResponse("Teacher profile not found.", status=404)
