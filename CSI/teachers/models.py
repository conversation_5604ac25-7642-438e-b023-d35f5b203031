from django.db import models
from django.contrib.auth.models import User
from students.models import Course, Student

# Create your models here.

class Teacher(models.Model):
    """Teacher profile model"""
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    employee_id = models.CharField(max_length=20, unique=True)
    department = models.CharField(max_length=100)
    hire_date = models.DateField()
    specialization = models.CharField(max_length=200, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.employee_id}"

class TeacherCourse(models.Model):
    """Teacher-Course assignment model"""
    teacher = models.ForeignKey(Teacher, on_delete=models.CASCADE)
    course = models.ForeignKey(Course, on_delete=models.CASCADE)
    semester = models.Char<PERSON>ield(max_length=20)
    year = models.IntegerField()
    is_active = models.<PERSON><PERSON>an<PERSON>ield(default=True)

    class Meta:
        unique_together = ['teacher', 'course', 'semester', 'year']

    def __str__(self):
        return f"{self.teacher.user.username} - {self.course.course_code} ({self.semester} {self.year})"
