from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse
from .models import Student, Course, Enrollment

# Create your views here.

@login_required
def student_dashboard(request):
    """Student dashboard view"""
    try:
        student = Student.objects.get(user=request.user)
        enrollments = Enrollment.objects.filter(student=student)
        context = {
            'student': student,
            'enrollments': enrollments,
        }
        return render(request, 'students/dashboard.html', context)
    except Student.DoesNotExist:
        return HttpResponse("Student profile not found.", status=404)

@login_required
def student_profile(request):
    """Student profile view"""
    try:
        student = Student.objects.get(user=request.user)
        context = {'student': student}
        return render(request, 'students/profile.html', context)
    except Student.DoesNotExist:
        return HttpResponse("Student profile not found.", status=404)

@login_required
def student_courses(request):
    """Student courses view"""
    try:
        student = Student.objects.get(user=request.user)
        enrollments = Enrollment.objects.filter(student=student)
        available_courses = Course.objects.filter(is_active=True)
        context = {
            'student': student,
            'enrollments': enrollments,
            'available_courses': available_courses,
        }
        return render(request, 'students/courses.html', context)
    except Student.DoesNotExist:
        return HttpResponse("Student profile not found.", status=404)

@login_required
def student_grades(request):
    """Student grades view"""
    try:
        student = Student.objects.get(user=request.user)
        enrollments = Enrollment.objects.filter(student=student, is_completed=True)
        context = {
            'student': student,
            'enrollments': enrollments,
        }
        return render(request, 'students/grades.html', context)
    except Student.DoesNotExist:
        return HttpResponse("Student profile not found.", status=404)
